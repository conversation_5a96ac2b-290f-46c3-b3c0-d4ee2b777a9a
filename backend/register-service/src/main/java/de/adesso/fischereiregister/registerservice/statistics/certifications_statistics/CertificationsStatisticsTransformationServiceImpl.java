package de.adesso.fischereiregister.registerservice.statistics.certifications_statistics;

import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Implementation of the CertificationsStatisticsTransformationService.
 * Transforms certification statistics view objects into domain model objects.
 */
@Service
@Slf4j
public class CertificationsStatisticsTransformationServiceImpl implements CertificationsStatisticsTransformationService {

    @Override
    public List<CertificationsStatistics> transformToCertificationsStatistics(List<CertificationsStatisticsView> statisticsViews, List<Integer> yearsToQuery, List<String> availableIssuers) {
        try {
            // Group statistics by year
            Map<Integer, List<CertificationsStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(CertificationsStatisticsView::getYear));

            // Transform each requested year's statistics
            List<CertificationsStatistics> result = new ArrayList<>();
            for (Integer year : yearsToQuery) {
                List<CertificationsStatisticsView> yearStats = statsByYear.get(year);
                List<CertificationsStatisticsDataEntry> dataEntries = new ArrayList<>();

                // Group existing data by issuer
                Map<String, Integer> countsByIssuer = new HashMap<>();
                if (yearStats != null && !yearStats.isEmpty()) {
                    countsByIssuer = yearStats.stream()
                            .collect(Collectors.groupingBy(
                                    CertificationsStatisticsView::getIssuer,
                                    Collectors.summingInt(CertificationsStatisticsView::getCount)
                            ));
                }

                // Create data entries for all available issuers
                for (String issuer : availableIssuers) {
                    int count = countsByIssuer.getOrDefault(issuer, 0);
                    dataEntries.add(new CertificationsStatisticsDataEntry(issuer, count));
                }

                // Create statistics object for this year
                result.add(new CertificationsStatistics(year, dataEntries));
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(CertificationsStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming certification statistics views: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform certification statistics views", e);
        }
    }
}
